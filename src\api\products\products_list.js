import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import "./products_list.css";

function Products() {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("name");
  const [filterBy, setFilterBy] = useState("all");
  const navigate = useNavigate();

  useEffect(() => {
    const token = localStorage.getItem("access_token");
    fetch("http://192.168.100.118:8000/api/products/", {
      headers: {
        ...(token ? { Authorization: `Bearer ${token}` } : {})
      }
    })
      .then((res) => {
        if (!res.ok) throw new Error("Unauthorized or error fetching products");
        return res.json();
      })
      .then((data) => {
        if (Array.isArray(data)) setProducts(data);
        else setProducts([]);
      })
      .catch((err) => setError(err))
      .finally(() => setLoading(false));
  }, []);

  // Filter and sort products
  const filteredAndSortedProducts = products
    .filter(product => {
      const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           product.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           product.sku.toLowerCase().includes(searchTerm.toLowerCase());

      if (filterBy === "all") return matchesSearch;
      if (filterBy === "available") return matchesSearch && product.is_available;
      if (filterBy === "out_of_stock") return matchesSearch && product.stock <= 0;
      if (filterBy === "featured") return matchesSearch && product.is_featured;
      return matchesSearch;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case "name":
          return a.name.localeCompare(b.name);
        case "price":
          return parseFloat(a.price) - parseFloat(b.price);
        case "stock":
          return a.stock - b.stock;
        case "created_at":
          return new Date(b.created_at) - new Date(a.created_at);
        default:
          return 0;
      }
    });

  const handleProductClick = (product) => {
    // Navigate to product detail page (we'll create this route later)
    navigate(`/products/${product.id}`, { state: { product } });
  };

  const formatPrice = (price, discountPrice) => {
    const originalPrice = parseFloat(price);
    const discount = discountPrice ? parseFloat(discountPrice) : null;

    if (discount && discount < originalPrice) {
      return (
        <div className="product-card__price-container">
          <span className="product-card__price product-card__price--discounted">
            ${originalPrice.toFixed(2)}
          </span>
          <span className="product-card__price product-card__price--current">
            ${discount.toFixed(2)}
          </span>
        </div>
      );
    }

    return (
      <span className="product-card__price product-card__price--current">
        ${originalPrice.toFixed(2)}
      </span>
    );
  };

  const getStockStatus = (stock, isAvailable) => {
    if (!isAvailable) return { text: "Unavailable", className: "unavailable" };
    if (stock <= 0) return { text: "Out of Stock", className: "out-of-stock" };
    if (stock <= 10) return { text: "Low Stock", className: "low-stock" };
    return { text: "In Stock", className: "in-stock" };
  };

  const getProductImage = (images) => {
    // Handle different image formats that might come from the API
    if (!images) return "https://via.placeholder.com/200x200/f1f5f9/64748b?text=No+Image";

    if (typeof images === "string") {
      try {
        const imageArray = JSON.parse(images);
        return imageArray.length > 0 ? imageArray[0] : "https://via.placeholder.com/200x200/f1f5f9/64748b?text=No+Image";
      } catch {
        return images.startsWith("http") ? images : "https://via.placeholder.com/200x200/f1f5f9/64748b?text=No+Image";
      }
    }

    if (Array.isArray(images) && images.length > 0) {
      return images[0];
    }

    return "https://via.placeholder.com/200x200/f1f5f9/64748b?text=No+Image";
  };

  if (loading) {
    return (
      <div className="products-container">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading products...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="products-container">
        <div className="error-container">
          <div className="error-icon">⚠️</div>
          <h2>Error Loading Products</h2>
          <p>Unable to fetch products. Please try again later.</p>
          <button
            className="retry-button"
            onClick={() => window.location.reload()}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="products-container">
      {/* Header Section */}
      <div className="products-header">
        <div className="products-header__title-section">
          <h1 className="products-header__title">Product Inventory</h1>
          <p className="products-header__subtitle">
            Manage and view your product catalog
          </p>
        </div>

        <div className="products-header__stats">
          <div className="stat-card">
            <div className="stat-card__value">{products.length}</div>
            <div className="stat-card__label">Total Products</div>
          </div>
          <div className="stat-card">
            <div className="stat-card__value">
              {products.filter(p => p.is_available).length}
            </div>
            <div className="stat-card__label">Available</div>
          </div>
          <div className="stat-card">
            <div className="stat-card__value">
              {products.filter(p => p.stock <= 0).length}
            </div>
            <div className="stat-card__label">Out of Stock</div>
          </div>
        </div>
      </div>

      {/* Controls Section */}
      <div className="products-controls">
        <div className="products-controls__search">
          <div className="search-input-container">
            <span className="search-icon">🔍</span>
            <input
              type="text"
              placeholder="Search products by name, description, or SKU..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
        </div>

        <div className="products-controls__filters">
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="control-select"
          >
            <option value="name">Sort by Name</option>
            <option value="price">Sort by Price</option>
            <option value="stock">Sort by Stock</option>
            <option value="created_at">Sort by Date Added</option>
          </select>

          <select
            value={filterBy}
            onChange={(e) => setFilterBy(e.target.value)}
            className="control-select"
          >
            <option value="all">All Products</option>
            <option value="available">Available Only</option>
            <option value="out_of_stock">Out of Stock</option>
            <option value="featured">Featured Products</option>
          </select>
        </div>
      </div>

      {/* Products Grid */}
      {filteredAndSortedProducts.length === 0 ? (
        <div className="empty-state">
          <div className="empty-state__icon">📦</div>
          <h2 className="empty-state__title">No Products Found</h2>
          <p className="empty-state__message">
            {searchTerm || filterBy !== "all"
              ? "Try adjusting your search or filter criteria."
              : "No products are available in your inventory."}
          </p>
        </div>
      ) : (
        <div className="products-grid">
          {filteredAndSortedProducts.map(product => {
            const stockStatus = getStockStatus(product.stock, product.is_available);

            return (
              <div
                key={product.id}
                className="product-card"
                onClick={() => handleProductClick(product)}
              >
                <div className="product-card__image-container">
                  <img
                    src={getProductImage(product.images)}
                    alt={product.name}
                    className="product-card__image"
                    onError={(e) => {
                      e.target.src = "https://via.placeholder.com/200x200/f1f5f9/64748b?text=No+Image";
                    }}
                  />
                  {product.is_featured && (
                    <div className="product-card__badge">Featured</div>
                  )}
                  <div className={`product-card__stock-status product-card__stock-status--${stockStatus.className}`}>
                    {stockStatus.text}
                  </div>
                </div>

                <div className="product-card__content">
                  <div className="product-card__header">
                    <h3 className="product-card__title">{product.name}</h3>
                    <span className="product-card__sku">SKU: {product.sku}</span>
                  </div>

                  <p className="product-card__description">
                    {product.description.length > 100
                      ? `${product.description.substring(0, 100)}...`
                      : product.description}
                  </p>

                  <div className="product-card__details">
                    <div className="product-card__price-section">
                      {formatPrice(product.price, product.discount_price)}
                    </div>

                    <div className="product-card__stock">
                      <span className="product-card__stock-label">Stock:</span>
                      <span className={`product-card__stock-value ${product.stock <= 10 ? 'low' : ''}`}>
                        {product.stock}
                      </span>
                    </div>
                  </div>

                  <div className="product-card__meta">
                    <span className="product-card__category">
                      {product.subcategory || "Uncategorized"}
                    </span>
                    {product.brand && (
                      <span className="product-card__brand">{product.brand}</span>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}

export default Products;

