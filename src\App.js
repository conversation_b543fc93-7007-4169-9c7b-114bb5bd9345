import {BrowserRouter as Router,Routes,Route} from 'react-router-dom';
import Header from './Components/Header';
import Home from './Components/Home';
import './App.css';
import Products from './api/products/products_list'
import ProductDetail from './api/products/product_detail'
import Loginpage from './Pages/loginpage';

function App() {
  return (
    <Router>
      <Header />
      <main className="main-content">
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/products" element={<Products />} />
          <Route path="/products/:id" element={<ProductDetail />} />
          <Route path="/login"    element={<Loginpage />} />
        </Routes>
      </main>
    </Router>
  );
}

export default App;