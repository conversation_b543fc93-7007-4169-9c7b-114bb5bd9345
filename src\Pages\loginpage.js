import React, { useState } from 'react'

function Loginpage() {
    const [username, setUsername] = useState("");
    const [password, setPassword] = useState("");
    const [error, setError] = useState("");

    const handleLogin = async (e) => {
        e.preventDefault();
        try {
            const response = await fetch("http://192.168.100.118:8000/api/token/", {
                method: "POST",
                headers: {
                    "Content-Type": 'application/json',
                },
                body: JSON.stringify({ username, password }),
            });
            console.log("Response stats:", response.status);
            if (!response.ok) {
                console.log("Login failed");
                throw new Error("Login failed");
            }
            const data = await response.json();
            // Save token to localStorage
            if (data.access) {
                localStorage.setItem("access_token", data.access);
            }
            // Optionally save refresh token
            if (data.refresh) {
                localStorage.setItem("refresh_token", data.refresh);
            }
            // Optionally redirect or update UI here
        } catch (err) {
            setError(err.message);
            console.log(err.message);
        }
    };

    return (
        <div>
            <form onSubmit={handleLogin}>
                <input
                type="text"
                placeholder="Username"
                value={username}
                onChange={e => setUsername(e.target.value)}
            />
            <input
                type="password"
                placeholder="Password"
                value={password}
                onChange={e => setPassword(e.target.value)}
            />
            <button type='submit' >Login</button>
            </form>
            {error && <div style={{ color: "red" }}>{error}</div>}
        </div>
    );
}

export default Loginpage
